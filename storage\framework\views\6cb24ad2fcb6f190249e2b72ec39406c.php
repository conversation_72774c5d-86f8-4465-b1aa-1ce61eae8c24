<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Create Exam','description' => 'Set up a new exam for students','backRoute' => route('admin.grading.exams.index'),'backLabel' => 'Back to Exams']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Create Exam','description' => 'Set up a new exam for students','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.grading.exams.index')),'back-label' => 'Back to Exams']); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Exam Form -->
    <div class="card">
        <form id="examForm" method="POST" action="<?php echo e(route('admin.grading.exams.store')); ?>" class="space-y-6">
            <?php echo csrf_field(); ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Exam Title *</label>
                    <input type="text" name="title" id="title"
                           class="form-input"
                           value="<?php echo e(old('title')); ?>"
                           placeholder="e.g., Midterm Examination" required>
                    <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Exam Code -->
                <div>
                    <label for="exam_code" class="block text-sm font-medium text-gray-700 mb-1">Exam Code *</label>
                    <input type="text" name="exam_code" id="exam_code"
                           class="form-input"
                           value="<?php echo e(old('exam_code')); ?>"
                           placeholder="e.g., MT2024" required>
                    <?php $__errorArgs = ['exam_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Academic Year -->
                <div>
                    <label for="academic_year_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Year *</label>
                    <select name="academic_year_id" id="academic_year_id" class="form-select" required>
                        <option value="">Select Academic Year</option>
                        <?php $__currentLoopData = $academicYears ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($year->id); ?>" <?php echo e(old('academic_year_id') == $year->id ? 'selected' : ''); ?>>
                                <?php echo e($year->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['academic_year_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Academic Term -->
                <div>
                    <label for="academic_term_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Term *</label>
                    <select name="academic_term_id" id="academic_term_id" class="form-select" required>
                        <option value="">Select Academic Term</option>
                        <?php $__currentLoopData = $academicTerms ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $term): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($term->id); ?>" <?php echo e(old('academic_term_id') == $term->id ? 'selected' : ''); ?>>
                                <?php echo e($term->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['academic_term_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Grade Category -->
                <div>
                    <label for="grade_category_id" class="block text-sm font-medium text-gray-700 mb-1">Grade Category *</label>
                    <select name="grade_category_id" id="grade_category_id" class="form-select" required>
                        <option value="">Select Grade Category</option>
                        <?php $__currentLoopData = $gradeCategories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" <?php echo e(old('grade_category_id') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?> (<?php echo e($category->weight); ?>%)
                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['grade_category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Target Classes -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Target Classes *</label>

                    <!-- Multiselect Search Component -->
                    <div x-data="multiselectSearch()" class="relative"
                         x-init="
                            items = [
                                <?php $__currentLoopData = $classes ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                {
                                    id: <?php echo e($class->id); ?>,
                                    name: '<?php echo e(addslashes($class->name . ' - ' . $class->section)); ?>',
                                    subtitle: '<?php echo e(addslashes('Grade ' . $class->grade_level . ' • ' . $class->academic_year)); ?>',
                                    searchText: '<?php echo e(addslashes($class->name . ' ' . $class->section . ' ' . $class->grade_level)); ?>'
                                },
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            ];
                            selected = <?php echo json_encode(old('target_classes', []), 512) ?>;
                            name = 'target_classes[]';
                            placeholder = 'Search and select target classes...';
                            init();
                         ">

                        <!-- Search Input -->
                        <div class="relative">
                            <input
                                type="text"
                                x-model="searchQuery"
                                @focus="showDropdown = true"
                                @click.away="showDropdown = false"
                                class="form-input w-full pr-10"
                                :placeholder="placeholder"
                                autocomplete="off"
                            >
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Selected Items -->
                        <div x-show="selectedItems.length > 0" class="mt-2 flex flex-wrap gap-2">
                            <template x-for="item in selectedItems" :key="item.id">
                                <span class="inline-flex items-center px-3 py-1 rounded-md text-sm bg-blue-100 text-blue-800">
                                    <span x-text="item.name"></span>
                                    <button type="button" @click="removeItem(item.id)" class="ml-2 text-blue-600 hover:text-blue-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </span>
                            </template>
                        </div>

                        <!-- Dropdown -->
                        <div x-show="showDropdown && filteredItems.length > 0"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                            <template x-for="item in filteredItems" :key="item.id">
                                <div @click="toggleItem(item)"
                                     class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50">
                                    <div class="flex items-center">
                                        <span class="font-medium block truncate" x-text="item.name"></span>
                                    </div>
                                    <span class="text-gray-500 block truncate text-sm" x-text="item.subtitle"></span>
                                </div>
                            </template>
                        </div>

                        <!-- Hidden inputs for form submission -->
                        <template x-for="item in selectedItems" :key="item.id">
                            <input type="hidden" :name="name" :value="item.id">
                        </template>
                    </div>

                    <?php $__errorArgs = ['target_classes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Exam Date -->
                <div>
                    <label for="exam_date" class="block text-sm font-medium text-gray-700 mb-1">Exam Date *</label>
                    <input type="date" name="exam_date" id="exam_date"
                           class="form-input"
                           value="<?php echo e(old('exam_date')); ?>" required>
                    <?php $__errorArgs = ['exam_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Total Marks -->
                <div>
                    <label for="total_marks" class="block text-sm font-medium text-gray-700 mb-1">Total Marks *</label>
                    <input type="number" name="total_marks" id="total_marks"
                           class="form-input"
                           value="<?php echo e(old('total_marks')); ?>"
                           min="1" step="0.1" required>
                    <?php $__errorArgs = ['total_marks'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Passing Marks -->
                <div>
                    <label for="passing_marks" class="block text-sm font-medium text-gray-700 mb-1">Passing Marks *</label>
                    <input type="number" name="passing_marks" id="passing_marks"
                           class="form-input"
                           value="<?php echo e(old('passing_marks')); ?>"
                           min="0" step="0.1" required>
                    <?php $__errorArgs = ['passing_marks'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Duration -->
                <div>
                    <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-1">Duration (Minutes)</label>
                    <input type="number" name="duration_minutes" id="duration_minutes"
                           class="form-input"
                           value="<?php echo e(old('duration_minutes')); ?>"
                           min="1" placeholder="e.g., 120">
                    <?php $__errorArgs = ['duration_minutes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

            <!-- Exam Subjects -->
            <div class="col-span-2">
                <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-medium text-gray-900">Exam Subjects</h4>
                        <button type="button" id="add-subject" class="btn-secondary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Subject
                        </button>
                    </div>

                    <div id="subjects-container" class="space-y-4">
                        <!-- Subject template will be added here -->
                    </div>

                    <?php $__errorArgs = ['subjects'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea name="description" id="description" rows="3"
                          class="form-textarea"
                          placeholder="Optional exam description"><?php echo e(old('description')); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Instructions -->
            <div>
                <label for="instructions" class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                <textarea name="instructions" id="instructions" rows="3"
                          class="form-textarea"
                          placeholder="Exam instructions for students"><?php echo e(old('instructions')); ?></textarea>
                <?php $__errorArgs = ['instructions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>



            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="<?php echo e(route('admin.grading.exams.index')); ?>" class="btn-cancel">
                    Cancel
                </a>
                <button type="button" onclick="showPublishModal()" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Exam
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Publish Modal -->
<div id="publishModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Publish Exam</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Would you like to publish this exam immediately? Published exams will be visible to students.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button onclick="submitForm(false)" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 mb-2">
                    Create as Draft
                </button>
                <button onclick="submitForm(true)" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    Create and Publish
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Publish Modal Functions
function showPublishModal() {
    document.getElementById('publishModal').classList.remove('hidden');
}

function hidePublishModal() {
    document.getElementById('publishModal').classList.add('hidden');
}

function submitForm(publish) {
    const form = document.getElementById('examForm');

    // Add hidden input for publish status
    const publishInput = document.createElement('input');
    publishInput.type = 'hidden';
    publishInput.name = 'is_published';
    publishInput.value = publish ? '1' : '0';
    form.appendChild(publishInput);

    // Submit the form
    form.submit();
}

// Close modal when clicking outside
document.getElementById('publishModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hidePublishModal();
    }
});

// Multiselect Search Component
function multiselectSearch() {
    return {
        items: [],
        selectedItems: [],
        searchQuery: '',
        showDropdown: false,
        name: 'items[]',
        placeholder: 'Search and select items...',
        selected: [],

        init() {
            // Initialize selected items based on this.selected
            if (this.selected && this.selected.length > 0) {
                this.selectedItems = this.items.filter(item =>
                    this.selected.includes(parseInt(item.id))
                );
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items.filter(item => !this.isSelected(item.id));
            }

            return this.items.filter(item => {
                const matchesSearch = item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
                const notSelected = !this.isSelected(item.id);
                return matchesSearch && notSelected;
            });
        },

        isSelected(itemId) {
            return this.selectedItems.some(item => item.id == itemId);
        },

        toggleItem(item) {
            if (this.isSelected(item.id)) {
                this.removeItem(item.id);
            } else {
                this.selectedItems.push(item);
                this.searchQuery = '';
                this.showDropdown = false;
            }
        },

        removeItem(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
        }
    };
}

document.addEventListener('DOMContentLoaded', function() {
    let subjectIndex = 0;
    const subjectsContainer = document.getElementById('subjects-container');
    const addSubjectBtn = document.getElementById('add-subject');

    // Add initial subject
    addSubject();

    addSubjectBtn.addEventListener('click', addSubject);

    function addSubject() {
        const subjectHtml = `
            <div class="subject-item border border-gray-200 rounded-lg p-4" data-index="${subjectIndex}">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-sm font-medium text-gray-900">Subject ${subjectIndex + 1}</h5>
                    <button type="button" class="remove-subject text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="Delete subject" ${subjectIndex === 0 ? 'style="display: none;"' : ''}>
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
                        <select name="subjects[${subjectIndex}][subject_id]" class="form-select" required>
                            <option value="">Select Subject</option>
                            <?php $__currentLoopData = $subjects ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($subject->id); ?>"><?php echo e($subject->name); ?> (<?php echo e($subject->subject_code); ?>)</option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Teacher</label>
                        <select name="subjects[${subjectIndex}][teacher_id]" class="form-select">
                            <option value="">Select Teacher</option>
                            <?php $__currentLoopData = $teachers ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($teacher->id); ?>"><?php echo e($teacher->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Total Marks *</label>
                        <input type="number" name="subjects[${subjectIndex}][subject_total_marks]"
                               class="form-input" min="1" step="0.1" required>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Passing Marks *</label>
                        <input type="number" name="subjects[${subjectIndex}][subject_passing_marks]"
                               class="form-input" min="0" step="0.1" required>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                        <input type="time" name="subjects[${subjectIndex}][subject_start_time]" class="form-input">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                        <input type="time" name="subjects[${subjectIndex}][subject_end_time]" class="form-input">
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject Instructions</label>
                        <textarea name="subjects[${subjectIndex}][subject_instructions]"
                                  class="form-textarea" rows="2"
                                  placeholder="Specific instructions for this subject"></textarea>
                    </div>
                </div>
            </div>
        `;

        subjectsContainer.insertAdjacentHTML('beforeend', subjectHtml);
        subjectIndex++;

        // Add event listener to remove button
        const removeButtons = subjectsContainer.querySelectorAll('.remove-subject');
        removeButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.subject-item').remove();
                updateSubjectNumbers();
            });
        });
    }

    function updateSubjectNumbers() {
        const subjects = subjectsContainer.querySelectorAll('.subject-item');
        subjects.forEach((subject, index) => {
            const title = subject.querySelector('h5');
            title.textContent = `Subject ${index + 1}`;

            // Show/hide remove button for first item
            const removeBtn = subject.querySelector('.remove-subject');
            if (index === 0) {
                removeBtn.style.display = 'none';
            } else {
                removeBtn.style.display = 'block';
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/exams/create.blade.php ENDPATH**/ ?>