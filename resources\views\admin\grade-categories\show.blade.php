@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Grade Category Details"
        description="View grade category information and usage"
        :back-route="route('admin.grading.grade-categories.index')"
        back-label="Back to Grade Categories">

        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.grading.grade-categories.edit', $gradeCategory) }}" class="btn-secondary" style="cursor: pointer;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Grade Category
            </a>
        </div>
    </x-page-header>

    <!-- Grade Category Information -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-purple-500 to-purple-600">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">{{ $gradeCategory->name }}</h1>
                    <p class="text-purple-100">{{ $gradeCategory->code }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full" style="background-color: {{ $gradeCategory->color }}"></div>
                    <span class="badge {{ $gradeCategory->is_active ? 'badge-green' : 'badge-red' }}">
                        {{ $gradeCategory->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Category Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Category Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $gradeCategory->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Code</label>
                        <p class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white" style="background-color: {{ $gradeCategory->color }}">
                                {{ $gradeCategory->code }}
                            </span>
                        </p>
                    </div>

                    @if($gradeCategory->description)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $gradeCategory->description }}</p>
                        </div>
                    @endif

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Weight Percentage</label>
                        <p class="mt-1 text-sm text-gray-900">{{ number_format($gradeCategory->weight_percentage, 1) }}%</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Sort Order</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $gradeCategory->sort_order }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $gradeCategory->created_at->format('M d, Y g:i A') }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $gradeCategory->updated_at->format('M d, Y g:i A') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Usage Statistics -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>

                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Exams:</span>
                        <span class="badge badge-blue">{{ $usage['exams'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Assignments:</span>
                        <span class="badge badge-indigo">{{ $usage['assignments'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Grades:</span>
                        <span class="badge badge-green">{{ $usage['grades'] }}</span>
                    </div>
                    <hr class="border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-900">Total Usage:</span>
                        <span class="badge badge-gray">{{ $usage['exams'] + $usage['assignments'] + $usage['grades'] }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>

                <div class="space-y-3">
                    <form method="POST" action="{{ route('admin.grading.grade-categories.toggle-status', $gradeCategory) }}" class="w-full toggle-category-status-form">
                        @csrf
                        <button type="button"
                                style="cursor: pointer;"
                                class="w-full {{ $gradeCategory->is_active ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white px-4 py-2 rounded-lg transition-colors text-center toggle-category-status-btn"
                                data-category-id="{{ $gradeCategory->id }}"
                                data-category-name="{{ $gradeCategory->name }}"
                                data-is-active="{{ $gradeCategory->is_active ? 'true' : 'false' }}">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($gradeCategory->is_active)
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2 4H7a2 2 0 01-2-2V8a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2z"></path>
                                @endif
                            </svg>
                            {{ $gradeCategory->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>

                    @if($usage['exams'] + $usage['assignments'] + $usage['grades'] == 0)
                        <form method="POST" action="{{ route('admin.grading.grade-categories.destroy', $gradeCategory) }}" class="w-full delete-category-form">
                            @csrf
                            @method('DELETE')
                            <button type="button"
                                    style="cursor: pointer;"
                                    class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors text-center delete-category-btn"
                                    data-category-id="{{ $gradeCategory->id }}"
                                    data-category-name="{{ $gradeCategory->name }}">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Category
                            </button>
                        </form>
                    @else
                        <button type="button"
                                class="w-full bg-gray-400 text-white px-4 py-2 rounded-lg text-center cursor-not-allowed"
                                disabled
                                title="Cannot delete category that is currently in use">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete (In Use)
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Handle toggle category status with custom modal confirmation
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-category-status-btn');
    const deleteButtons = document.querySelectorAll('.delete-category-btn');

    // Toggle status functionality
    toggleButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const categoryId = this.getAttribute('data-category-id');
            const categoryName = this.getAttribute('data-category-name');
            const isActive = this.getAttribute('data-is-active') === 'true';

            const action = isActive ? 'deactivate' : 'activate';
            const actionText = action.charAt(0).toUpperCase() + action.slice(1);

            const confirmed = await confirmModal({
                title: `${actionText} Grade Category`,
                message: `Are you sure you want to ${action} the grade category "${categoryName}"?`,
                confirmText: actionText,
                cancelText: 'Cancel',
                type: isActive ? 'warning' : 'success'
            });

            if (confirmed) {
                // Submit the form
                const form = this.closest('.toggle-category-status-form');
                form.submit();
            }
        });
    });

    // Delete functionality
    deleteButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const categoryId = this.getAttribute('data-category-id');
            const categoryName = this.getAttribute('data-category-name');

            const confirmed = await confirmModal({
                title: 'Delete Grade Category',
                message: `Are you sure you want to delete the grade category "${categoryName}"? This action cannot be undone.`,
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger'
            });

            if (confirmed) {
                // Submit the form
                const form = this.closest('.delete-category-form');
                form.submit();
            }
        });
    });
});
</script>
@endpush
